<div class="bg-white p-4 rounded-lg shadow">
    <!-- Loading Indicator -->
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Filtros</h3>
        
        <!-- Contenedor fijo para spinner -->
        <div class="flex items-center space-x-2">
            <div class="flex-none w-5 h-5 flex items-center justify-center">
                <div wire:loading wire:target="selectedCategories,selectedTags,toggleCategory,toggleTag,clearFilters"
                     class="animate-spin text-blue-500">
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg"
                         fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10"
                                stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor"
                              d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"/>
                    </svg>
                </div>
            </div>
            
            <!-- Clear Filters Button -->
            <!--[if BLOCK]><![endif]--><?php if(!empty($selectedCategories) || !empty($selectedTags)): ?>
                <button wire:click="clearFilters"
                        class="text-sm text-red-600 hover:text-red-800 font-medium">
                    Limpiar filtros
                </button>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </div>

    <div class="space-y-6">
        <!-- Categories Section -->
        <!--[if BLOCK]><![endif]--><?php if($categories->count() > 0): ?>
            <div>
                <h4 class="text-sm font-medium text-gray-700 mb-3">Categorías</h4>
                <div class="space-y-2">
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div>
                            <!-- Parent Category -->
                            <label class="flex items-center">
                                <input type="checkbox"
                                       wire:model.live="selectedCategories"
                                       value="<?php echo e($category->id); ?>"
                                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700 font-medium"><?php echo e($category->name); ?></span>
                            </label>
                            
                            <!-- Subcategories -->
                            <!--[if BLOCK]><![endif]--><?php if($category->children->count() > 0): ?>
                                <div class="ml-6 mt-2 space-y-1">
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $category->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <label class="flex items-center">
                                            <input type="checkbox"
                                                   wire:model.live="selectedCategories"
                                                   value="<?php echo e($subcategory->id); ?>"
                                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                            <span class="ml-2 text-sm text-gray-600"><?php echo e($subcategory->name); ?></span>
                                        </label>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <!-- Tags Section -->
        <!--[if BLOCK]><![endif]--><?php if($tags->count() > 0): ?>
            <div>
                <h4 class="text-sm font-medium text-gray-700 mb-3">Etiquetas</h4>
                <div class="flex flex-wrap gap-2">
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <button wire:click="toggleTag(<?php echo e($tag->id); ?>)"
                                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium transition-colors duration-200
                                       <?php if(in_array($tag->id, $selectedTags)): ?>
                                           bg-blue-100 text-blue-800 border border-blue-200
                                       <?php else: ?>
                                           bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200
                                       <?php endif; ?>">
                            <?php echo e($tag->name); ?>

                            <!--[if BLOCK]><![endif]--><?php if(in_array($tag->id, $selectedTags)): ?>
                                <svg class="ml-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </button>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <!-- Active Filters Summary -->
        <!--[if BLOCK]><![endif]--><?php if(!empty($selectedCategories) || !empty($selectedTags)): ?>
            <div class="pt-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">
                        <?php echo e(count($selectedCategories) + count($selectedTags)); ?> filtro(s) activo(s)
                    </span>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/livewire/products/product-filters.blade.php ENDPATH**/ ?>