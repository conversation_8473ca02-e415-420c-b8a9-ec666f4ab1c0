<?php

namespace App\Livewire\Products;

use Livewire\Component;
use LBCDev\Ecommerce\Models\Category;
use LBCDev\Ecommerce\Models\Tag;

class ProductFilters extends Component
{
    public $selectedCategories = [];
    public $selectedTags = [];

    public function mount($initialCategories = [], $initialTags = [])
    {
        $this->selectedCategories = $initialCategories;
        $this->selectedTags = $initialTags;
    }

    public function updatedSelectedCategories()
    {
        $this->dispatch('products-loading-start');
        // Filtrar valores vacíos y reindexar el array
        $this->selectedCategories = array_values(array_filter($this->selectedCategories));
        $this->dispatch('filtersUpdated', $this->selectedCategories, $this->selectedTags);
        $this->dispatch('products-loading-end');
    }

    public function updatedSelectedTags()
    {
        $this->dispatch('products-loading-start');
        // Filtrar valores vacíos y reindexar el array
        $this->selectedTags = array_values(array_filter($this->selectedTags));
        $this->dispatch('filtersUpdated', $this->selectedCategories, $this->selectedTags);
        $this->dispatch('products-loading-end');
    }

    public function clearFilters()
    {
        $this->dispatch('products-loading-start');
        $this->selectedCategories = [];
        $this->selectedTags = [];
        $this->dispatch('filtersUpdated', [], []);
        $this->dispatch('products-loading-end');
    }

    public function toggleTag($tagId)
    {
        $this->dispatch('products-loading-start');

        if (in_array($tagId, $this->selectedTags)) {
            $this->selectedTags = array_values(array_diff($this->selectedTags, [$tagId]));
        } else {
            $this->selectedTags[] = $tagId;
        }

        $this->dispatch('filtersUpdated', $this->selectedCategories, $this->selectedTags);
        $this->dispatch('products-loading-end');
    }

    public function getCategoriesProperty()
    {
        $rootId = Category::root()->id;

        return Category::where('parent_id', $rootId)
            ->with(['children' => function ($query) use ($rootId) {
                $query->where('parent_id', '!=', $rootId)->orderBy('name');
            }])
            ->orderBy('name')
            ->get();
    }

    public function getTagsProperty()
    {
        return Tag::orderBy('name')->get();
    }

    public function render()
    {
        return view('livewire.products.product-filters', [
            'categories' => $this->categories,
            'tags' => $this->tags,
        ]);
    }
}
