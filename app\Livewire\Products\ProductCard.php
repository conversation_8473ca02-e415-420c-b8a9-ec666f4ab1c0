<?php

namespace App\Livewire\Products;

use Livewire\Component;
use LBCDev\Ecommerce\Models\Product;

class ProductCard extends Component
{
    public $productId;
    public $name;
    public $shortDescription;
    public $price;
    public $categories;
    public $tags;

    public function mount(Product $product)
    {
        $this->productId = $product->id;
        $this->name = $product->name;
        $this->shortDescription = $product->short_description;
        $this->price = $product->price;
        $this->categories = $product->categoriesRelation->pluck('name')->toArray();
        $this->tags = $product->tagsRelation->pluck('name')->toArray();
    }

    public function addToCart($quantity = 1)
    {
        $this->dispatch('addToCartFromCard', $this->productId, $quantity);
    }

    public function render()
    {
        return view('livewire.products.product-card');
    }
}
